import os
import re
import shutil
from docx import Document
from PIL import Image
from io import BytesIO

# === 路径设置 ===
input_docx_path = r"./电子与信息工程学院-计算机科学与技术（大数据方向）-22计算机（大数据）-22201010112-李明昊.docx"  # 替换为实际路径
output_dir = "extracted_images"


# === 创建图片保存目录 ===
if os.path.exists(output_dir):
    shutil.rmtree(output_dir)
os.makedirs(output_dir)

# === 加载 Word 文档 ===
doc = Document(input_docx_path)

# === 初始化变量 ===
question_number = None
# 正则匹配题号
question_pattern = re.compile(r'^(\d+)[\.、]?\s*(p\d+\s*)?(第?\d*题)?\s*(学生答案)?', re.IGNORECASE)

question_image_counter = {}

# === 遍历段落提取图片 ===
for para in doc.paragraphs:
    text = para.text.strip()

    # 识别题号段
    match = question_pattern.match(text)
    if match:
        question_number = int(match.group(1))
        question_image_counter[question_number] = 1

    # 提取该段落中的图片
    for run in para.runs:
        if run._element.xpath('.//pic:pic'):
            for drawing in run._element.xpath('.//pic:pic'):
                blip = drawing.xpath('.//a:blip')
                if blip and question_number is not None:
                    # 获取图片数据
                    rEmbed = blip[0].get("{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed")
                    image_part = doc.part.related_parts[rEmbed]
                    image_bytes = image_part.blob
                    image = Image.open(BytesIO(image_bytes))

                    # 构造保存文件名：题号_序号.png
                    img_seq = question_image_counter[question_number]
                    image_filename = f"{question_number}_{img_seq}.png"
                    image_path = os.path.join(output_dir, image_filename)
                    image.save(image_path)

                    # 序号 +1
                    question_image_counter[question_number] += 1

print("✅ 图片提取完成，保存目录：", output_dir)
